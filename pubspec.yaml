name: money_track
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.1+1

environment:
  sdk: ">=3.4.0 <4.0.0"


dependencies:
  cloud_firestore: ^6.0.0
  connectivity_plus: ^6.1.5
  cupertino_icons: ^1.0.8
  equatable: ^2.0.5
  firebase_auth: ^6.0.1
  firebase_core: ^4.0.0
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.0
  # flutter_local_notifications: ^16.3.2
  get_it: ^8.0.3
  google_fonts: ^6.2.1
  hive_ce: ^2.11.3
  hive_ce_flutter: ^2.3.1
  intl: ^0.20.2
  lottie: null
  page_transition: ^2.1.0
  provider: ^6.0.5
  rive: ^0.13.4
  share_plus: ^11.0.0
  shared_preferences: ^2.3.4
  smooth_page_indicator: ^1.2.1
  svg_flutter: ^0.0.1
  syncfusion_flutter_charts: ^29.1.40
  url_launcher: ^6.1.8
  uuid: ^4.4.0

dev_dependencies:
  build_runner: ^2.4.14
  flutter_launcher_icons: ^0.14.2
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  # mockito: ^5.4.4
  integration_test:
    sdk: flutter
  bloc_test: ^10.0.0
  hive_ce_generator: ^1.9.3

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/1024.png"
  adaptive_icon_background: "assets/icons/play_store_512.png"
  adaptive_icon_foreground: "assets/icons/play_store_512.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/lottie/
    - assets/rive/
    - assets/svg/
    - assets/svg/home/
    - assets/svg/category/
    - assets/svg/common/
  #   - images/a_dot_ham.jpeg

