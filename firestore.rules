rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user owns the resource
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    // Helper function to validate transaction data with enhanced checks
    function isValidTransaction(transaction) {
      return transaction.keys().hasAll(['id', 'amount', 'categoryType', 'transactionType', 'category', 'userId']) &&
             transaction.amount is number &&
             transaction.amount > 0 &&
             transaction.amount <= 999999999 &&
             (transaction.notes == null || (transaction.notes is string && transaction.notes.size() <= 500)) &&
             transaction.categoryType is string &&
             transaction.categoryType in ['income', 'expense'] &&
             transaction.transactionType is string &&
             transaction.transactionType in ['income', 'expense'] &&
             transaction.userId is string &&
             transaction.userId.size() > 0 &&
             transaction.category is map &&
             transaction.category.keys().hasAll(['id', 'categoryName', 'userId']) &&
             transaction.category.userId == transaction.userId &&
             (transaction.version == null || (transaction.version is int && transaction.version >= 0)) &&
             (transaction.date == null || transaction.date is timestamp) &&
             (transaction.createdAt == null || transaction.createdAt is timestamp) &&
             (transaction.updatedAt == null || transaction.updatedAt is timestamp);
    }
    
    // Helper function to validate category data with enhanced checks
    function isValidCategory(category) {
      return category.keys().hasAll(['id', 'categoryName', 'categoryType', 'type', 'userId']) &&
             category.categoryName is string &&
             category.categoryName.size() > 0 &&
             category.categoryName.size() <= 100 &&
             category.categoryType is string &&
             category.categoryType in ['income', 'expense'] &&
             category.type is string &&
             category.type in ['income', 'expense'] &&
             category.userId is string &&
             category.userId.size() > 0 &&
             (category.isDeleted == null || category.isDeleted is bool) &&
             (category.version == null || (category.version is int && category.version >= 0)) &&
             (category.createdAt == null || category.createdAt is timestamp) &&
             (category.updatedAt == null || category.updatedAt is timestamp);
    }

    // Helper function to check if user has valid authentication token
    function hasValidAuth() {
      return request.auth != null &&
             request.auth.uid != null &&
             request.auth.uid.size() > 0 &&
             request.auth.token != null;
    }

    // Helper function to validate version increment for conflict resolution
    function isValidVersionUpdate(newVersion, oldVersion) {
      return newVersion == null ||
             oldVersion == null ||
             newVersion > oldVersion;
    }
    
    // Transactions collection rules with enhanced validation
    match /transactions/{transactionId} {
      // Allow read if user has valid authentication and owns the transaction
      allow read: if hasValidAuth() &&
                  resource != null &&
                  isOwner(resource.data.userId);

      // Allow create if user has valid auth, owns the transaction, and data is valid
      allow create: if hasValidAuth() &&
                   isOwner(request.resource.data.userId) &&
                   isValidTransaction(request.resource.data) &&
                   request.resource.data.userId == request.auth.uid &&
                   transactionId == request.resource.data.id;

      // Allow update if user has valid auth, owns the transaction, data is valid,
      // version is properly incremented, and user ID doesn't change
      allow update: if hasValidAuth() &&
                   resource != null &&
                   isOwner(resource.data.userId) &&
                   isOwner(request.resource.data.userId) &&
                   isValidTransaction(request.resource.data) &&
                   isValidVersionUpdate(request.resource.data.version, resource.data.version) &&
                   request.resource.data.userId == resource.data.userId &&
                   request.resource.data.id == resource.data.id;

      // Allow delete if user has valid auth and owns the transaction
      allow delete: if hasValidAuth() &&
                   resource != null &&
                   isOwner(resource.data.userId);
    }
    
    // Categories collection rules with enhanced validation
    match /categories/{categoryId} {
      // Allow read if user has valid authentication and owns the category
      allow read: if hasValidAuth() &&
                  resource != null &&
                  isOwner(resource.data.userId);

      // Allow create if user has valid auth, owns the category, and data is valid
      allow create: if hasValidAuth() &&
                   isOwner(request.resource.data.userId) &&
                   isValidCategory(request.resource.data) &&
                   request.resource.data.userId == request.auth.uid &&
                   categoryId == request.resource.data.id;

      // Allow update if user has valid auth, owns the category, data is valid,
      // version is properly incremented, and user ID doesn't change
      allow update: if hasValidAuth() &&
                   resource != null &&
                   isOwner(resource.data.userId) &&
                   isOwner(request.resource.data.userId) &&
                   isValidCategory(request.resource.data) &&
                   isValidVersionUpdate(request.resource.data.version, resource.data.version) &&
                   request.resource.data.userId == resource.data.userId &&
                   request.resource.data.id == resource.data.id;

      // Allow delete if user has valid auth and owns the category
      allow delete: if hasValidAuth() &&
                   resource != null &&
                   isOwner(resource.data.userId);
    }
    
    // User profile collection (if needed in the future)
    match /users/{userId} {
      // Allow read/write only for the user's own profile
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}

// Additional rules for Firebase Storage (if used for file uploads)
// service firebase.storage {
//   match /b/{bucket}/o {
//     match /users/{userId}/{allPaths=**} {
//       // Allow read/write only for authenticated users accessing their own files
//       allow read, write: if request.auth != null && request.auth.uid == userId;
//     }
//   }
// }
