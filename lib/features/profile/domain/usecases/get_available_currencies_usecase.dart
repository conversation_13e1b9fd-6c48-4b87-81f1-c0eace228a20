import 'package:money_track/core/error/result.dart';
import 'package:money_track/core/use_cases/use_case.dart';
import 'package:money_track/features/profile/domain/entities/currency_entity.dart';
import 'package:money_track/features/profile/domain/repositories/currency_repository.dart';

/// Use case for getting all available currencies
class GetAvailableCurrenciesUseCase implements UseCase<Result<List<CurrencyEntity>>, NoParams> {
  final CurrencyRepository repository;

  GetAvailableCurrenciesUseCase(this.repository);

  @override
  Future<Result<List<CurrencyEntity>>> call({NoParams? params}) {
    return repository.getAvailableCurrencies();
  }
}
