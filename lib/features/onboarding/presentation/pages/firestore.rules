rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to check group membership
    function isGroupMember(groupId) {
      return get(/databases/$(database)/documents/groups/$(groupId)).data.members.hasAny([request.auth.uid]);
    }

    // Users can read/update their own profile
    match /users/{userId} {
      allow read, update: if request.auth.uid == userId;
    }

    // Group data is only accessible by its members
    match /groups/{groupId} {
      allow read, update: if isGroupMember(groupId);
      allow create: if request.auth.uid in request.resource.data.members;

      // Expenses and balances inherit permissions from the parent group
      match /expenses/{expenseId} {
        allow read, create: if isGroupMember(groupId);
      }

      match /balances/{balanceId} {
        allow read, write: if isGroupMember(groupId);
      }
    }
  }
}
