import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:money_track/core/error/failures.dart';
import 'package:money_track/core/utils/auth_state_validator.dart';
import 'package:money_track/core/utils/circuit_breaker.dart';

/// Enhanced error handling for sync operations with user-friendly feedback
class EnhancedSyncErrorHandler {
  final AuthStateValidator _authValidator;
  final CircuitBreakerManager _circuitBreakerManager;

  // Error tracking
  int _consecutiveAuthErrors = 0;
  int _consecutiveNetworkErrors = 0;
  DateTime? _lastErrorTime;

  // User feedback callbacks
  Function(String message, SyncErrorSeverity severity)? onUserFeedback;
  Function(SyncErrorAction action)? onErrorAction;

  EnhancedSyncErrorHandler({
    required AuthStateValidator authValidator,
    required CircuitBreakerManager circuitBreakerManager,
    this.onUserFeedback,
    this.onErrorAction,
  })  : _authValidator = authValidator,
        _circuitBreakerManager = circuitBreakerManager;

  /// Handle sync operation errors with comprehensive analysis and user feedback
  Future<SyncErrorResult> handleSyncError(
    dynamic error,
    String operationName,
    Map<String, dynamic>? context,
  ) async {
    _lastErrorTime = DateTime.now();

    log('Handling sync error for $operationName: $error',
        name: 'EnhancedSyncErrorHandler');

    // Analyze the error type and context
    final errorAnalysis = await _analyzeError(error, operationName, context);

    // Provide user feedback
    _provideUserFeedback(errorAnalysis);

    // Determine recovery action
    final recoveryAction = _determineRecoveryAction(errorAnalysis);

    // Execute recovery if possible
    final recoveryResult =
        await _executeRecovery(recoveryAction, errorAnalysis);

    return SyncErrorResult(
      errorAnalysis: errorAnalysis,
      recoveryAction: recoveryAction,
      recoverySuccessful: recoveryResult,
      shouldRetry: _shouldRetryOperation(errorAnalysis),
      userMessage: _generateUserMessage(errorAnalysis),
    );
  }

  /// Analyze error to determine type, cause, and severity
  Future<SyncErrorAnalysis> _analyzeError(
    dynamic error,
    String operationName,
    Map<String, dynamic>? context,
  ) async {
    final analysis = SyncErrorAnalysis(
      originalError: error,
      operationName: operationName,
      timestamp: DateTime.now(),
      context: context ?? {},
    );

    // Categorize error type
    if (error is FirebaseAuthException) {
      analysis.errorType = SyncErrorType.authentication;
      analysis.severity = SyncErrorSeverity.high;
      _consecutiveAuthErrors++;

      // Check auth state
      final authValidation = await _authValidator.validateAuthState();
      analysis.authValidation = authValidation;
    } else if (error is FirebaseException) {
      final errorCode = error.code.toLowerCase();

      if (errorCode.contains('permission-denied')) {
        analysis.errorType = SyncErrorType.permission;
        analysis.severity = SyncErrorSeverity.high;
        _consecutiveAuthErrors++;

        // Validate auth state for permission errors
        final authValidation = await _authValidator.validateAuthState();
        analysis.authValidation = authValidation;
      } else if (errorCode.contains('unavailable') ||
          errorCode.contains('deadline-exceeded')) {
        analysis.errorType = SyncErrorType.network;
        analysis.severity = SyncErrorSeverity.medium;
        _consecutiveNetworkErrors++;
      } else if (errorCode.contains('not-found')) {
        analysis.errorType = SyncErrorType.dataConsistency;
        analysis.severity = SyncErrorSeverity.medium;
      } else {
        analysis.errorType = SyncErrorType.firestore;
        analysis.severity = SyncErrorSeverity.medium;
      }
    } else if (error is CircuitBreakerOpenException) {
      analysis.errorType = SyncErrorType.circuitBreaker;
      analysis.severity = SyncErrorSeverity.low;
    } else if (error.toString().toLowerCase().contains('network')) {
      analysis.errorType = SyncErrorType.network;
      analysis.severity = SyncErrorSeverity.medium;
      _consecutiveNetworkErrors++;
    } else {
      analysis.errorType = SyncErrorType.unknown;
      analysis.severity = SyncErrorSeverity.medium;
    }

    // Check circuit breaker status
    analysis.circuitBreakerStatus = _circuitBreakerManager.getAllStatus();

    return analysis;
  }

  /// Provide user-friendly feedback based on error analysis
  void _provideUserFeedback(SyncErrorAnalysis analysis) {
    String message;
    SyncErrorSeverity severity = analysis.severity;

    switch (analysis.errorType) {
      case SyncErrorType.authentication:
        if (_consecutiveAuthErrors > 3) {
          message =
              "Authentication issues detected. Please sign out and sign in again.";
          severity = SyncErrorSeverity.high;
        } else {
          message = "Authentication error. Attempting to refresh...";
          severity = SyncErrorSeverity.medium;
        }
        break;

      case SyncErrorType.permission:
        message =
            "Permission denied. Your session may have expired. Please sign in again.";
        severity = SyncErrorSeverity.high;
        break;

      case SyncErrorType.network:
        if (_consecutiveNetworkErrors > 5) {
          message =
              "Persistent network issues. Please check your internet connection.";
          severity = SyncErrorSeverity.high;
        } else {
          message = "Network error. Retrying...";
          severity = SyncErrorSeverity.low;
        }
        break;

      case SyncErrorType.circuitBreaker:
        message = "Service temporarily unavailable. Will retry automatically.";
        severity = SyncErrorSeverity.low;
        break;

      case SyncErrorType.dataConsistency:
        message = "Data synchronization issue detected. Resolving...";
        severity = SyncErrorSeverity.medium;
        break;

      case SyncErrorType.firestore:
        message = "Database error occurred. Please try again.";
        severity = SyncErrorSeverity.medium;
        break;

      case SyncErrorType.unknown:
        message = "An unexpected error occurred. Please try again.";
        severity = SyncErrorSeverity.medium;
        break;
    }

    onUserFeedback?.call(message, severity);
  }

  /// Determine the appropriate recovery action
  SyncErrorAction _determineRecoveryAction(SyncErrorAnalysis analysis) {
    switch (analysis.errorType) {
      case SyncErrorType.authentication:
      case SyncErrorType.permission:
        if (analysis.authValidation?.tokenValid == false) {
          return SyncErrorAction.refreshToken;
        } else if (_consecutiveAuthErrors > 3) {
          return SyncErrorAction.requireReauth;
        } else {
          return SyncErrorAction.refreshToken;
        }

      case SyncErrorType.network:
        return SyncErrorAction.retryWithBackoff;

      case SyncErrorType.circuitBreaker:
        return SyncErrorAction.waitAndRetry;

      case SyncErrorType.dataConsistency:
        return SyncErrorAction.forceSync;

      case SyncErrorType.firestore:
        return SyncErrorAction.retryWithBackoff;

      case SyncErrorType.unknown:
        return SyncErrorAction.retryWithBackoff;
    }
  }

  /// Execute recovery action
  Future<bool> _executeRecovery(
    SyncErrorAction action,
    SyncErrorAnalysis analysis,
  ) async {
    try {
      switch (action) {
        case SyncErrorAction.refreshToken:
          log('Attempting token refresh', name: 'EnhancedSyncErrorHandler');
          final success = await _authValidator.refreshAuthToken();
          if (success) {
            _consecutiveAuthErrors = 0;
          }
          return success;

        case SyncErrorAction.requireReauth:
          log('Requiring user re-authentication',
              name: 'EnhancedSyncErrorHandler');
          onErrorAction?.call(SyncErrorAction.requireReauth);
          return false;

        case SyncErrorAction.retryWithBackoff:
          log('Scheduling retry with backoff',
              name: 'EnhancedSyncErrorHandler');
          return true;

        case SyncErrorAction.waitAndRetry:
          log('Waiting for circuit breaker reset',
              name: 'EnhancedSyncErrorHandler');
          return true;

        case SyncErrorAction.forceSync:
          log('Requesting force sync', name: 'EnhancedSyncErrorHandler');
          onErrorAction?.call(SyncErrorAction.forceSync);
          return true;
      }
    } catch (e) {
      log('Recovery action failed: $e', name: 'EnhancedSyncErrorHandler');
      return false;
    }
  }

  /// Determine if operation should be retried
  bool _shouldRetryOperation(SyncErrorAnalysis analysis) {
    switch (analysis.errorType) {
      case SyncErrorType.authentication:
        return _consecutiveAuthErrors <= 3;
      case SyncErrorType.permission:
        return false; // Don't retry permission errors
      case SyncErrorType.network:
        return _consecutiveNetworkErrors <= 10;
      case SyncErrorType.circuitBreaker:
        return false; // Circuit breaker handles retry
      case SyncErrorType.dataConsistency:
        return true;
      case SyncErrorType.firestore:
        return true;
      case SyncErrorType.unknown:
        return true;
    }
  }

  /// Generate user-friendly error message
  String _generateUserMessage(SyncErrorAnalysis analysis) {
    switch (analysis.errorType) {
      case SyncErrorType.authentication:
      case SyncErrorType.permission:
        return "Please sign in again to continue syncing your data.";
      case SyncErrorType.network:
        return "Check your internet connection and try again.";
      case SyncErrorType.circuitBreaker:
        return "Service is temporarily unavailable. Please wait a moment.";
      case SyncErrorType.dataConsistency:
        return "Synchronizing your data. This may take a moment.";
      case SyncErrorType.firestore:
        return "Database temporarily unavailable. Please try again.";
      case SyncErrorType.unknown:
        return "An error occurred. Please try again.";
    }
  }

  /// Reset error counters on successful operation
  void resetErrorCounters() {
    _consecutiveAuthErrors = 0;
    _consecutiveNetworkErrors = 0;
    _lastErrorTime = null;
  }

  /// Get current error statistics
  Map<String, dynamic> getErrorStats() {
    return {
      'consecutiveAuthErrors': _consecutiveAuthErrors,
      'consecutiveNetworkErrors': _consecutiveNetworkErrors,
      'lastErrorTime': _lastErrorTime?.toIso8601String(),
      'circuitBreakerStatus': _circuitBreakerManager.getAllStatus(),
    };
  }
}

/// Types of sync errors
enum SyncErrorType {
  authentication,
  permission,
  network,
  firestore,
  circuitBreaker,
  dataConsistency,
  unknown,
}

/// Severity levels for sync errors
enum SyncErrorSeverity {
  low, // Minor issues, automatic retry
  medium, // Noticeable issues, user notification
  high, // Critical issues, user action required
}

/// Recovery actions for sync errors
enum SyncErrorAction {
  refreshToken,
  requireReauth,
  retryWithBackoff,
  waitAndRetry,
  forceSync,
}

/// Comprehensive error analysis result
class SyncErrorAnalysis {
  final dynamic originalError;
  final String operationName;
  final DateTime timestamp;
  final Map<String, dynamic> context;

  SyncErrorType errorType = SyncErrorType.unknown;
  SyncErrorSeverity severity = SyncErrorSeverity.medium;
  AuthValidationResult? authValidation;
  Map<String, dynamic>? circuitBreakerStatus;

  SyncErrorAnalysis({
    required this.originalError,
    required this.operationName,
    required this.timestamp,
    required this.context,
  });

  Map<String, dynamic> toMap() {
    return {
      'errorType': errorType.name,
      'severity': severity.name,
      'operationName': operationName,
      'timestamp': timestamp.toIso8601String(),
      'context': context,
      'authValidation': authValidation?.getSummary(),
      'circuitBreakerStatus': circuitBreakerStatus,
      'originalError': originalError.toString(),
    };
  }
}

/// Result of sync error handling
class SyncErrorResult {
  final SyncErrorAnalysis errorAnalysis;
  final SyncErrorAction recoveryAction;
  final bool recoverySuccessful;
  final bool shouldRetry;
  final String userMessage;

  SyncErrorResult({
    required this.errorAnalysis,
    required this.recoveryAction,
    required this.recoverySuccessful,
    required this.shouldRetry,
    required this.userMessage,
  });

  Map<String, dynamic> toMap() {
    return {
      'errorAnalysis': errorAnalysis.toMap(),
      'recoveryAction': recoveryAction.name,
      'recoverySuccessful': recoverySuccessful,
      'shouldRetry': shouldRetry,
      'userMessage': userMessage,
    };
  }
}
