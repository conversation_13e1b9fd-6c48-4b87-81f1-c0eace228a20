import 'dart:developer';
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:money_track/core/utils/auth_state_validator.dart';
import 'package:money_track/core/utils/circuit_breaker.dart';

/// Comprehensive logging system for sync operations and debugging
class SyncLogger {
  static const String _loggerName = 'SyncLogger';
  
  // Log levels
  static const int _levelDebug = 0;
  static const int _levelInfo = 1;
  static const int _levelWarning = 2;
  static const int _levelError = 3;
  static const int _levelCritical = 4;
  
  // Current log level (can be configured)
  static int _currentLogLevel = _levelDebug;
  
  // Log storage for debugging
  static final List<SyncLogEntry> _logHistory = [];
  static const int _maxLogEntries = 1000;
  
  /// Set the minimum log level
  static void setLogLevel(int level) {
    _currentLogLevel = level;
    log('Log level set to $level', name: _loggerName);
  }
  
  /// Log authentication state changes
  static void logAuthStateChange(User? user, String context) {
    final entry = SyncLogEntry(
      level: _levelInfo,
      category: 'AUTH_STATE',
      message: 'Auth state changed: ${user?.uid ?? 'null'}',
      context: {
        'userId': user?.uid,
        'email': user?.email,
        'emailVerified': user?.emailVerified,
        'isAnonymous': user?.isAnonymous,
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    _addLogEntry(entry);
    
    if (_currentLogLevel <= _levelInfo) {
      log('${entry.message} - Context: $context', name: _loggerName);
    }
  }
  
  /// Log authentication validation results
  static void logAuthValidation(AuthValidationResult result, String operation) {
    final entry = SyncLogEntry(
      level: result.isValid ? _levelInfo : _levelWarning,
      category: 'AUTH_VALIDATION',
      message: 'Auth validation for $operation: ${result.isValid ? 'VALID' : 'INVALID'}',
      context: {
        'operation': operation,
        'isValid': result.isValid,
        'userId': result.userId,
        'tokenValid': result.tokenValid,
        'tokenNearExpiry': result.tokenNearExpiry,
        'firestorePermissions': result.firestorePermissionsValid,
        'errors': result.errors,
        'warnings': result.warnings,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    _addLogEntry(entry);
    
    if (_currentLogLevel <= (result.isValid ? _levelInfo : _levelWarning)) {
      log('${entry.message} - ${result.getSummary()}', name: _loggerName);
    }
  }
  
  /// Log sync operation start
  static void logSyncOperationStart(String operation, Map<String, dynamic>? params) {
    final entry = SyncLogEntry(
      level: _levelDebug,
      category: 'SYNC_OPERATION',
      message: 'Starting sync operation: $operation',
      context: {
        'operation': operation,
        'parameters': params ?? {},
        'startTime': DateTime.now().toIso8601String(),
      },
    );
    
    _addLogEntry(entry);
    
    if (_currentLogLevel <= _levelDebug) {
      log('${entry.message} - Params: ${jsonEncode(params ?? {})}', name: _loggerName);
    }
  }
  
  /// Log sync operation completion
  static void logSyncOperationComplete(
    String operation,
    Duration duration,
    Map<String, dynamic>? result,
  ) {
    final entry = SyncLogEntry(
      level: _levelInfo,
      category: 'SYNC_OPERATION',
      message: 'Completed sync operation: $operation in ${duration.inMilliseconds}ms',
      context: {
        'operation': operation,
        'duration': duration.inMilliseconds,
        'result': result ?? {},
        'endTime': DateTime.now().toIso8601String(),
      },
    );
    
    _addLogEntry(entry);
    
    if (_currentLogLevel <= _levelInfo) {
      log('${entry.message} - Result: ${jsonEncode(result ?? {})}', name: _loggerName);
    }
  }
  
  /// Log sync operation error
  static void logSyncOperationError(
    String operation,
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  ) {
    final entry = SyncLogEntry(
      level: _levelError,
      category: 'SYNC_ERROR',
      message: 'Sync operation failed: $operation - $error',
      context: {
        'operation': operation,
        'error': error.toString(),
        'errorType': error.runtimeType.toString(),
        'stackTrace': stackTrace?.toString(),
        'context': context ?? {},
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    _addLogEntry(entry);
    
    if (_currentLogLevel <= _levelError) {
      log('${entry.message}', name: _loggerName, error: error, stackTrace: stackTrace);
    }
  }
  
  /// Log circuit breaker state changes
  static void logCircuitBreakerStateChange(
    String operationName,
    CircuitBreakerState oldState,
    CircuitBreakerState newState,
    int failureCount,
  ) {
    final entry = SyncLogEntry(
      level: newState == CircuitBreakerState.open ? _levelWarning : _levelInfo,
      category: 'CIRCUIT_BREAKER',
      message: 'Circuit breaker state change: $operationName ($oldState -> $newState)',
      context: {
        'operationName': operationName,
        'oldState': oldState.toString(),
        'newState': newState.toString(),
        'failureCount': failureCount,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    _addLogEntry(entry);
    
    if (_currentLogLevel <= (newState == CircuitBreakerState.open ? _levelWarning : _levelInfo)) {
      log('${entry.message} - Failures: $failureCount', name: _loggerName);
    }
  }
  
  /// Log conflict resolution
  static void logConflictResolution(
    String dataType,
    String itemId,
    String resolution,
    Map<String, dynamic>? details,
  ) {
    final entry = SyncLogEntry(
      level: _levelInfo,
      category: 'CONFLICT_RESOLUTION',
      message: 'Conflict resolved: $dataType/$itemId using $resolution',
      context: {
        'dataType': dataType,
        'itemId': itemId,
        'resolution': resolution,
        'details': details ?? {},
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    _addLogEntry(entry);
    
    if (_currentLogLevel <= _levelInfo) {
      log('${entry.message} - Details: ${jsonEncode(details ?? {})}', name: _loggerName);
    }
  }
  
  /// Log performance metrics
  static void logPerformanceMetric(
    String metric,
    double value,
    String unit,
    Map<String, dynamic>? context,
  ) {
    final entry = SyncLogEntry(
      level: _levelDebug,
      category: 'PERFORMANCE',
      message: 'Performance metric: $metric = $value $unit',
      context: {
        'metric': metric,
        'value': value,
        'unit': unit,
        'context': context ?? {},
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
    
    _addLogEntry(entry);
    
    if (_currentLogLevel <= _levelDebug) {
      log('${entry.message}', name: _loggerName);
    }
  }
  
  /// Add log entry to history
  static void _addLogEntry(SyncLogEntry entry) {
    _logHistory.add(entry);
    
    // Maintain log history size
    if (_logHistory.length > _maxLogEntries) {
      _logHistory.removeAt(0);
    }
  }
  
  /// Get log history for debugging
  static List<SyncLogEntry> getLogHistory({
    String? category,
    int? minLevel,
    DateTime? since,
  }) {
    return _logHistory.where((entry) {
      if (category != null && entry.category != category) return false;
      if (minLevel != null && entry.level < minLevel) return false;
      if (since != null && entry.timestamp.isBefore(since)) return false;
      return true;
    }).toList();
  }
  
  /// Export logs for debugging
  static String exportLogs({
    String? category,
    int? minLevel,
    DateTime? since,
  }) {
    final logs = getLogHistory(category: category, minLevel: minLevel, since: since);
    final buffer = StringBuffer();
    
    buffer.writeln('=== SYNC LOGS EXPORT ===');
    buffer.writeln('Generated: ${DateTime.now().toIso8601String()}');
    buffer.writeln('Total entries: ${logs.length}');
    buffer.writeln('');
    
    for (final entry in logs) {
      buffer.writeln('${entry.timestamp.toIso8601String()} [${_levelToString(entry.level)}] ${entry.category}: ${entry.message}');
      if (entry.context.isNotEmpty) {
        buffer.writeln('  Context: ${jsonEncode(entry.context)}');
      }
      buffer.writeln('');
    }
    
    return buffer.toString();
  }
  
  /// Clear log history
  static void clearLogs() {
    _logHistory.clear();
    log('Log history cleared', name: _loggerName);
  }
  
  /// Get log statistics
  static Map<String, dynamic> getLogStats() {
    final stats = <String, int>{};
    final categoryStats = <String, int>{};
    
    for (final entry in _logHistory) {
      final levelName = _levelToString(entry.level);
      stats[levelName] = (stats[levelName] ?? 0) + 1;
      categoryStats[entry.category] = (categoryStats[entry.category] ?? 0) + 1;
    }
    
    return {
      'totalEntries': _logHistory.length,
      'levelStats': stats,
      'categoryStats': categoryStats,
      'oldestEntry': _logHistory.isNotEmpty ? _logHistory.first.timestamp.toIso8601String() : null,
      'newestEntry': _logHistory.isNotEmpty ? _logHistory.last.timestamp.toIso8601String() : null,
    };
  }
  
  /// Convert log level to string
  static String _levelToString(int level) {
    switch (level) {
      case _levelDebug: return 'DEBUG';
      case _levelInfo: return 'INFO';
      case _levelWarning: return 'WARNING';
      case _levelError: return 'ERROR';
      case _levelCritical: return 'CRITICAL';
      default: return 'UNKNOWN';
    }
  }
}

/// Log entry structure
class SyncLogEntry {
  final int level;
  final String category;
  final String message;
  final Map<String, dynamic> context;
  final DateTime timestamp;
  
  SyncLogEntry({
    required this.level,
    required this.category,
    required this.message,
    required this.context,
  }) : timestamp = DateTime.now();
  
  Map<String, dynamic> toMap() {
    return {
      'level': level,
      'category': category,
      'message': message,
      'context': context,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
