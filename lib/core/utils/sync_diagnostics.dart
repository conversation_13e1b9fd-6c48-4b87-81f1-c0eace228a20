import 'dart:developer';
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:money_track/core/utils/auth_state_validator.dart';
import 'package:money_track/core/utils/circuit_breaker.dart';
import 'package:money_track/core/utils/sync_logger.dart';

/// Comprehensive diagnostic tool for sync system debugging
class SyncDiagnostics {
  final AuthStateValidator _authValidator;
  final CircuitBreakerManager _circuitBreakerManager;
  final FirebaseAuth _firebaseAuth;
  final FirebaseFirestore _firestore;

  SyncDiagnostics({
    required AuthStateValidator authValidator,
    required CircuitBreakerManager circuitBreakerManager,
    required FirebaseAuth firebaseAuth,
    required FirebaseFirestore firestore,
  })  : _authValidator = authValidator,
        _circuitBreakerManager = circuitBreakerManager,
        _firebaseAuth = firebaseAuth,
        _firestore = firestore;

  /// Run comprehensive diagnostic check
  Future<SyncDiagnosticReport> runDiagnostics() async {
    log('Starting comprehensive sync diagnostics', name: 'SyncDiagnostics');

    final report = SyncDiagnosticReport();

    try {
      // 1. Authentication diagnostics
      report.authDiagnostics = await _runAuthDiagnostics();

      // 2. Firestore connectivity and permissions
      report.firestoreDiagnostics = await _runFirestoreDiagnostics();

      // 3. Circuit breaker status
      report.circuitBreakerDiagnostics = _runCircuitBreakerDiagnostics();

      // 4. Log analysis
      report.logAnalysis = _runLogAnalysis();

      // 5. Performance metrics
      report.performanceMetrics = _runPerformanceAnalysis();

      // 6. Overall health assessment
      report.overallHealth = _assessOverallHealth(report);

      log('Sync diagnostics completed', name: 'SyncDiagnostics');
    } catch (e) {
      log('Error running diagnostics: $e', name: 'SyncDiagnostics');
      report.diagnosticError = e.toString();
    }

    return report;
  }

  /// Run authentication-specific diagnostics
  Future<AuthDiagnostics> _runAuthDiagnostics() async {
    final authDiag = AuthDiagnostics();

    try {
      // Basic auth state
      final user = _firebaseAuth.currentUser;
      authDiag.hasCurrentUser = user != null;
      authDiag.userId = user?.uid;
      authDiag.userEmail = user?.email;
      authDiag.emailVerified = user?.emailVerified ?? false;

      if (user != null) {
        // Token validation
        try {
          final idTokenResult = await user.getIdTokenResult();
          authDiag.tokenValid = true;
          authDiag.tokenExpirationTime = idTokenResult.expirationTime;
          authDiag.tokenIssuedAt = idTokenResult.issuedAtTime;
          authDiag.authProvider = idTokenResult.signInProvider;

          // Check token expiry
          final now = DateTime.now();
          if (idTokenResult.expirationTime != null) {
            final timeToExpiry = idTokenResult.expirationTime!.difference(now);
            authDiag.tokenExpiresInMinutes = timeToExpiry.inMinutes;
            authDiag.tokenNearExpiry = timeToExpiry.inMinutes < 5;
          }
        } catch (e) {
          authDiag.tokenValid = false;
          authDiag.tokenError = e.toString();
        }

        // Comprehensive validation
        authDiag.validationResult = await _authValidator.validateAuthState();
      }
    } catch (e) {
      authDiag.error = e.toString();
    }

    return authDiag;
  }

  /// Run Firestore-specific diagnostics
  Future<FirestoreDiagnostics> _runFirestoreDiagnostics() async {
    final firestoreDiag = FirestoreDiagnostics();

    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        firestoreDiag.error = 'No authenticated user for Firestore test';
        return firestoreDiag;
      }

      // Test basic connectivity
      final startTime = DateTime.now();

      try {
        // Test categories collection read
        final categoriesQuery = await _firestore
            .collection('categories')
            .where('userId', isEqualTo: user.uid)
            .limit(1)
            .get();

        firestoreDiag.categoriesReadable = true;
        firestoreDiag.categoriesCount = categoriesQuery.docs.length;
      } catch (e) {
        firestoreDiag.categoriesReadable = false;
        firestoreDiag.categoriesError = e.toString();
      }

      try {
        // Test transactions collection read
        final transactionsQuery = await _firestore
            .collection('transactions')
            .where('userId', isEqualTo: user.uid)
            .limit(1)
            .get();

        firestoreDiag.transactionsReadable = true;
        firestoreDiag.transactionsCount = transactionsQuery.docs.length;
      } catch (e) {
        firestoreDiag.transactionsReadable = false;
        firestoreDiag.transactionsError = e.toString();
      }

      // Test write permissions (create a test document)
      try {
        final testDocRef =
            _firestore.collection('_diagnostics').doc('test_${user.uid}');
        await testDocRef.set({
          'userId': user.uid,
          'timestamp': FieldValue.serverTimestamp(),
          'test': true,
        });

        // Clean up test document
        await testDocRef.delete();

        firestoreDiag.writePermissions = true;
      } catch (e) {
        firestoreDiag.writePermissions = false;
        firestoreDiag.writeError = e.toString();
      }

      final endTime = DateTime.now();
      firestoreDiag.responseTimeMs =
          endTime.difference(startTime).inMilliseconds;
    } catch (e) {
      firestoreDiag.error = e.toString();
    }

    return firestoreDiag;
  }

  /// Run circuit breaker diagnostics
  CircuitBreakerDiagnostics _runCircuitBreakerDiagnostics() {
    final cbDiag = CircuitBreakerDiagnostics();

    try {
      final status = _circuitBreakerManager.getAllStatus();
      cbDiag.circuitBreakerStatus = status;

      final openBreakers = _circuitBreakerManager.getOpenCircuitBreakers();
      cbDiag.openCircuitBreakers = openBreakers;
      cbDiag.hasOpenCircuitBreakers = openBreakers.isNotEmpty;
    } catch (e) {
      cbDiag.error = e.toString();
    }

    return cbDiag;
  }

  /// Analyze recent logs for patterns
  LogAnalysis _runLogAnalysis() {
    final logAnalysis = LogAnalysis();

    try {
      final recentLogs = SyncLogger.getLogHistory(
        since: DateTime.now().subtract(Duration(hours: 1)),
      );

      logAnalysis.totalRecentLogs = recentLogs.length;

      // Count errors by category
      final errorsByCategory = <String, int>{};
      final warningsByCategory = <String, int>{};

      for (final log in recentLogs) {
        if (log.level >= 3) {
          // Error level
          errorsByCategory[log.category] =
              (errorsByCategory[log.category] ?? 0) + 1;
        } else if (log.level >= 2) {
          // Warning level
          warningsByCategory[log.category] =
              (warningsByCategory[log.category] ?? 0) + 1;
        }
      }

      logAnalysis.errorsByCategory = errorsByCategory;
      logAnalysis.warningsByCategory = warningsByCategory;
      logAnalysis.totalErrors =
          errorsByCategory.values.fold(0, (a, b) => a + b);
      logAnalysis.totalWarnings =
          warningsByCategory.values.fold(0, (a, b) => a + b);

      // Look for specific patterns
      final authErrors = recentLogs
          .where((log) =>
              log.category == 'AUTH_VALIDATION' || log.category == 'AUTH_STATE')
          .length;
      logAnalysis.authRelatedIssues = authErrors;

      final syncErrors = recentLogs
          .where((log) =>
              log.category == 'SYNC_ERROR' || log.category == 'SYNC_OPERATION')
          .length;
      logAnalysis.syncRelatedIssues = syncErrors;
    } catch (e) {
      logAnalysis.error = e.toString();
    }

    return logAnalysis;
  }

  /// Analyze performance metrics
  PerformanceMetrics _runPerformanceAnalysis() {
    final perfMetrics = PerformanceMetrics();

    try {
      final perfLogs = SyncLogger.getLogHistory(
        category: 'PERFORMANCE',
        since: DateTime.now().subtract(Duration(hours: 1)),
      );

      perfMetrics.totalMetrics = perfLogs.length;

      // Calculate averages for common metrics
      final syncDurations = <double>[];
      final responseTimes = <double>[];

      for (final log in perfLogs) {
        final context = log.context;
        if (context['metric'] == 'sync_duration' && context['value'] is num) {
          syncDurations.add((context['value'] as num).toDouble());
        } else if (context['metric'] == 'response_time' &&
            context['value'] is num) {
          responseTimes.add((context['value'] as num).toDouble());
        }
      }

      if (syncDurations.isNotEmpty) {
        perfMetrics.averageSyncDuration =
            syncDurations.reduce((a, b) => a + b) / syncDurations.length;
      }

      if (responseTimes.isNotEmpty) {
        perfMetrics.averageResponseTime =
            responseTimes.reduce((a, b) => a + b) / responseTimes.length;
      }
    } catch (e) {
      perfMetrics.error = e.toString();
    }

    return perfMetrics;
  }

  /// Assess overall system health
  OverallHealth _assessOverallHealth(SyncDiagnosticReport report) {
    final health = OverallHealth();

    // Calculate health score (0-100)
    int score = 100;
    final issues = <String>[];

    // Authentication health
    if (report.authDiagnostics?.hasCurrentUser != true) {
      score -= 30;
      issues.add('No authenticated user');
    } else if (report.authDiagnostics?.tokenValid != true) {
      score -= 25;
      issues.add('Invalid authentication token');
    } else if (report.authDiagnostics?.tokenNearExpiry == true) {
      score -= 10;
      issues.add('Authentication token near expiry');
    }

    // Firestore health
    if (report.firestoreDiagnostics?.categoriesReadable != true) {
      score -= 20;
      issues.add('Cannot read categories from Firestore');
    }
    if (report.firestoreDiagnostics?.transactionsReadable != true) {
      score -= 20;
      issues.add('Cannot read transactions from Firestore');
    }
    if (report.firestoreDiagnostics?.writePermissions != true) {
      score -= 15;
      issues.add('No write permissions to Firestore');
    }

    // Circuit breaker health
    if (report.circuitBreakerDiagnostics?.hasOpenCircuitBreakers == true) {
      score -= 15;
      issues.add('Circuit breakers are open');
    }

    // Log analysis health
    if ((report.logAnalysis?.totalErrors ?? 0) > 10) {
      score -= 10;
      issues.add('High error rate in recent logs');
    }

    health.healthScore = score.clamp(0, 100);
    health.issues = issues;

    if (score >= 90) {
      health.status = 'EXCELLENT';
    } else if (score >= 70) {
      health.status = 'GOOD';
    } else if (score >= 50) {
      health.status = 'FAIR';
    } else if (score >= 30) {
      health.status = 'POOR';
    } else {
      health.status = 'CRITICAL';
    }

    return health;
  }

  /// Generate diagnostic report as JSON
  String exportDiagnosticReport(SyncDiagnosticReport report) {
    return jsonEncode(report.toMap());
  }
}

/// Comprehensive diagnostic report
class SyncDiagnosticReport {
  AuthDiagnostics? authDiagnostics;
  FirestoreDiagnostics? firestoreDiagnostics;
  CircuitBreakerDiagnostics? circuitBreakerDiagnostics;
  LogAnalysis? logAnalysis;
  PerformanceMetrics? performanceMetrics;
  OverallHealth? overallHealth;
  String? diagnosticError;
  final DateTime timestamp = DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'authDiagnostics': authDiagnostics?.toMap(),
      'firestoreDiagnostics': firestoreDiagnostics?.toMap(),
      'circuitBreakerDiagnostics': circuitBreakerDiagnostics?.toMap(),
      'logAnalysis': logAnalysis?.toMap(),
      'performanceMetrics': performanceMetrics?.toMap(),
      'overallHealth': overallHealth?.toMap(),
      'diagnosticError': diagnosticError,
    };
  }
}

/// Authentication diagnostics
class AuthDiagnostics {
  bool hasCurrentUser = false;
  String? userId;
  String? userEmail;
  bool emailVerified = false;
  bool tokenValid = false;
  DateTime? tokenExpirationTime;
  DateTime? tokenIssuedAt;
  String? authProvider;
  int? tokenExpiresInMinutes;
  bool tokenNearExpiry = false;
  String? tokenError;
  AuthValidationResult? validationResult;
  String? error;

  Map<String, dynamic> toMap() {
    return {
      'hasCurrentUser': hasCurrentUser,
      'userId': userId,
      'userEmail': userEmail,
      'emailVerified': emailVerified,
      'tokenValid': tokenValid,
      'tokenExpirationTime': tokenExpirationTime?.toIso8601String(),
      'tokenIssuedAt': tokenIssuedAt?.toIso8601String(),
      'authProvider': authProvider,
      'tokenExpiresInMinutes': tokenExpiresInMinutes,
      'tokenNearExpiry': tokenNearExpiry,
      'tokenError': tokenError,
      'validationResult': validationResult?.getSummary(),
      'error': error,
    };
  }
}

/// Firestore diagnostics
class FirestoreDiagnostics {
  bool categoriesReadable = false;
  bool transactionsReadable = false;
  bool writePermissions = false;
  int? categoriesCount;
  int? transactionsCount;
  int? responseTimeMs;
  String? categoriesError;
  String? transactionsError;
  String? writeError;
  String? error;

  Map<String, dynamic> toMap() {
    return {
      'categoriesReadable': categoriesReadable,
      'transactionsReadable': transactionsReadable,
      'writePermissions': writePermissions,
      'categoriesCount': categoriesCount,
      'transactionsCount': transactionsCount,
      'responseTimeMs': responseTimeMs,
      'categoriesError': categoriesError,
      'transactionsError': transactionsError,
      'writeError': writeError,
      'error': error,
    };
  }
}

/// Circuit breaker diagnostics
class CircuitBreakerDiagnostics {
  Map<String, dynamic>? circuitBreakerStatus;
  List<String> openCircuitBreakers = [];
  bool hasOpenCircuitBreakers = false;
  String? error;

  Map<String, dynamic> toMap() {
    return {
      'circuitBreakerStatus': circuitBreakerStatus,
      'openCircuitBreakers': openCircuitBreakers,
      'hasOpenCircuitBreakers': hasOpenCircuitBreakers,
      'error': error,
    };
  }
}

/// Log analysis results
class LogAnalysis {
  int totalRecentLogs = 0;
  int totalErrors = 0;
  int totalWarnings = 0;
  int authRelatedIssues = 0;
  int syncRelatedIssues = 0;
  Map<String, int> errorsByCategory = {};
  Map<String, int> warningsByCategory = {};
  String? error;

  Map<String, dynamic> toMap() {
    return {
      'totalRecentLogs': totalRecentLogs,
      'totalErrors': totalErrors,
      'totalWarnings': totalWarnings,
      'authRelatedIssues': authRelatedIssues,
      'syncRelatedIssues': syncRelatedIssues,
      'errorsByCategory': errorsByCategory,
      'warningsByCategory': warningsByCategory,
      'error': error,
    };
  }
}

/// Performance metrics
class PerformanceMetrics {
  int totalMetrics = 0;
  double? averageSyncDuration;
  double? averageResponseTime;
  String? error;

  Map<String, dynamic> toMap() {
    return {
      'totalMetrics': totalMetrics,
      'averageSyncDuration': averageSyncDuration,
      'averageResponseTime': averageResponseTime,
      'error': error,
    };
  }
}

/// Overall health assessment
class OverallHealth {
  String status = 'UNKNOWN';
  int healthScore = 0;
  List<String> issues = [];

  Map<String, dynamic> toMap() {
    return {
      'status': status,
      'healthScore': healthScore,
      'issues': issues,
    };
  }
}
