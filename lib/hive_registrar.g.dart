// Generated by Hive CE
// Do not modify
// Check in to version control

import 'package:hive_ce/hive.dart';
import 'package:money_track/data/models/category_model.dart';
import 'package:money_track/data/models/sync/sync_operation_model.dart';
import 'package:money_track/data/models/transaction_model.dart';
import 'package:money_track/features/budget/data/models/budget_model.dart';
import 'package:money_track/features/profile/data/models/currency_model.dart';

extension HiveRegistrar on HiveInterface {
  void registerAdapters() {
    registerAdapter(BudgetModelAdapter());
    registerAdapter(BudgetPeriodTypeAdapter());
    registerAdapter(CategoryModelAdapter());
    registerAdapter(CategoryTypeAdapter());
    registerAdapter(CurrencyModelAdapter());
    registerAdapter(SyncDataTypeAdapter());
    registerAdapter(SyncOperationModelAdapter());
    registerAdapter(SyncOperationTypeAdapter());
    registerAdapter(TransactionModelAdapter());
    registerAdapter(TransactionTypeAdapter());
  }
}

extension IsolatedHiveRegistrar on IsolatedHiveInterface {
  void registerAdapters() {
    registerAdapter(BudgetModelAdapter());
    registerAdapter(BudgetPeriodTypeAdapter());
    registerAdapter(CategoryModelAdapter());
    registerAdapter(CategoryTypeAdapter());
    registerAdapter(CurrencyModelAdapter());
    registerAdapter(SyncDataTypeAdapter());
    registerAdapter(SyncOperationModelAdapter());
    registerAdapter(SyncOperationTypeAdapter());
    registerAdapter(TransactionModelAdapter());
    registerAdapter(TransactionTypeAdapter());
  }
}
